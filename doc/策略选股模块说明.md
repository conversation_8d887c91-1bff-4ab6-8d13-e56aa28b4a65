# 策略选股模块说明

## 概述

A股选股系统的策略选股模块提供了基于技术指标的智能选股功能，支持多种选股策略，能够自动筛选符合条件的股票并提供详细的分析报告。

## 核心特性

### 🎯 多策略支持
- **技术反转策略**: 基于RSI、布林带、成交量等技术指标识别反转机会
- **成交量异常策略**: 识别成交量异常放大的股票
- **可扩展架构**: 支持自定义选股策略

### 📊 智能筛选
- **候选股票池控制**: 支持控制选择部分股票或全部股票进行策略筛选
- **评分机制**: 综合多个指标进行评分排序
- **ST股票过滤**: 自动过滤ST、*ST、PT等风险股票

### 🔧 灵活配置
- **参数可调**: 策略参数可根据市场情况调整
- **结果限制**: 可设置最大选股数量
- **详细输出**: 提供选股原因和技术指标详情

## 可用策略

### 1. 技术反转策略 (technical_reversal)

#### 策略逻辑
基于以下技术指标识别超跌反弹机会：
- **RSI指标**: 14日RSI作为评分因子
- **布林带突破**: 价格突破布林带下轨（必要条件）
- **成交量放大**: 成交量1.5-2倍放大（必要条件）

#### 选股条件
1. **必要条件**:
   - 布林带下轨突破
   - 成交量放大（1.5-2倍）
2. **评分因子**:
   - RSI值越低评分越高
   - 成交量放大倍数越高评分越高

#### 适用场景
- 识别超跌反弹机会
- 短期技术性反转
- 市场调整后的反弹

### 2. 成交量异常策略 (volume_anomaly)

#### 策略逻辑
识别成交量异常放大的股票：
- **成交量比较**: 与历史平均成交量对比
- **放大倍数**: 成交量显著放大
- **价格配合**: 价格表现配合成交量变化

#### 选股条件
1. **成交量异常**: 成交量显著高于历史平均
2. **价格表现**: 价格有一定涨幅配合
3. **持续性**: 成交量放大具有一定持续性

#### 适用场景
- 识别资金关注度突然提升的股票
- 捕捉主力资金动向
- 发现潜在热点股票

## 使用方法

### 基本命令

#### 查看可用策略
```bash
python run_selection.py info
```

#### 执行选股策略
```bash
# 执行技术反转策略
python run_selection.py select --strategy technical_reversal

# 执行成交量异常策略
python run_selection.py select --strategy volume_anomaly
```

### 高级参数

#### 候选股票池控制
```bash
# 使用指定的候选股票池
python run_selection.py select --strategy technical_reversal --candidate-pool 1000

# 使用所有股票（默认）
python run_selection.py select --strategy technical_reversal --candidate-pool 0
```

#### 结果数量控制
```bash
# 限制最多选出10只股票
python run_selection.py select --strategy technical_reversal --max-results 10
```

## 输出结果

### 选股结果格式
```json
{
  "strategy": "technical_reversal",
  "execution_time": "2024-12-06 10:30:00",
  "total_candidates": 5000,
  "selected_count": 5,
  "results": [
    {
      "stock_code": "000001",
      "stock_name": "平安银行",
      "score": 85.5,
      "price": 12.50,
      "reason": "布林带下轨突破，成交量放大2.1倍，RSI=25.3",
      "indicators": {
        "rsi": 25.3,
        "volume_ratio": 2.1,
        "bb_position": -0.05
      }
    }
  ]
}
```

### 结果说明
- **score**: 策略评分（0-100分）
- **reason**: 选股原因说明
- **indicators**: 关键技术指标值
- **price**: 当前股价
- **stock_name**: 股票名称

## 技术实现

### 策略管理器 (StrategyManager)
```python
class StrategyManager:
    """策略管理器"""
    
    def get_available_strategies(self) -> List[str]:
        """获取可用策略列表"""
    
    def execute_strategy(self, strategy_name: str, **kwargs) -> List[Dict]:
        """执行指定策略"""
```

### 策略接口 (ISelectionStrategy)
```python
class ISelectionStrategy(ABC):
    """选股策略抽象接口"""
    
    @abstractmethod
    def select_stocks(self, **kwargs) -> List[Dict]:
        """执行选股逻辑"""
    
    @abstractmethod
    def get_strategy_name(self) -> str:
        """获取策略名称"""
    
    @abstractmethod
    def get_strategy_description(self) -> str:
        """获取策略描述"""
```

### 策略配置
每个策略都有独立的配置参数：
```python
# 技术反转策略配置
{
    'rsi_period': 14,           # RSI计算周期
    'bb_period': 20,            # 布林带计算周期
    'volume_period': 5,         # 成交量比较周期
    'volume_threshold': 1.5,    # 成交量放大阈值
    'max_results': 20           # 最大选股数量
}
```

## 扩展开发

### 添加新策略
1. 继承 `ISelectionStrategy` 接口
2. 实现必要的方法
3. 在 `StrategyManager` 中注册策略

### 策略开发示例
```python
class CustomStrategy(ISelectionStrategy):
    def __init__(self, data_access: IDataAccess):
        self.data_access = data_access
    
    def select_stocks(self, **kwargs) -> List[Dict]:
        # 实现选股逻辑
        pass
    
    def get_strategy_name(self) -> str:
        return "custom_strategy"
    
    def get_strategy_description(self) -> str:
        return "自定义选股策略"
```

## 注意事项

1. **数据依赖**: 策略执行需要完整的历史交易数据
2. **计算时间**: 复杂策略可能需要较长计算时间
3. **市场适应性**: 策略参数需要根据市场情况调整
4. **风险控制**: 选股结果仅供参考，投资需谨慎

## 版本信息

- **模块版本**: v2.0
- **更新日期**: 2024年12月
- **主要特性**: 多策略支持、智能评分、候选池控制
